/* eslint-disable react-native/no-inline-styles */
import {
  ActivityIndicator,
  Pressable,
  ScrollView,
  Text,
  View,
} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import ScreenHeader from '../../../../Screen/Layout/header';
import {navigateBack} from '../../../../router/router';
import {
  AppButton,
  Checkbox,
  FBottomSheet,
  hideBottomSheet,
  ListTile,
  Winicon,
} from 'wini-mobile-components';
import WScreenFooter from '../../../../Screen/Layout/footer';
import {TypoSkin} from '../../../../assets/skin/typography';
import {useEffect, useMemo, useRef, useState} from 'react';
import {RadioButton} from 'react-native-paper';
import PagerView from 'react-native-pager-view';
import {useRoute} from '@react-navigation/native';
import {examDA} from '../../da';
import {SafeAreaView} from 'react-native-safe-area-context';

export default function HistoryTryingTest() {
  const pagerRef = useRef<PagerView>(null); // Ref for PagerView
  const [currentPage, setCurrentPage] = useState(0); // Track current page
  const [listQuestion, setlistQuestion] = useState<Array<any>>([]);
  const [loading, setloading] = useState(true);
  const examda = new examDA();
  const next = () => {
    if (pagerRef.current) {
      pagerRef.current.setPage(currentPage + 1); // Navigate to the next page
      setCurrentPage(currentPage + 1);
    }
  };

  const previous = () => {
    if (pagerRef.current) {
      pagerRef.current.setPage(currentPage - 1); // Navigate to the previous page
      setCurrentPage(currentPage - 1);
    }
  };
  const route = useRoute<any>();
  const {id} = route.params;

  useEffect(() => {
    getData();
  }, []);
  const getData = async () => {
    const result = await examda.getListQuestionbyTestId(id);
    if (result) {
      const list = await Promise.all(
        result.data.map(async (item: any) => {
          //lấy danh sách đáp án
          const ListAnswer = await examda.getAnswerHistoryTest(item.Id);

          return {
            ...item,
            lstAnswer: ListAnswer?.data?.map((ans: any) => {
              if (item.AnswerId) {
                if (item.AnswerId.includes(ans.AnswerId)) {
                  return {
                    ...ans,
                    choose: true,
                  };
                } else {
                  return {...ans, choose: false};
                }
              } else {
                return {...ans, choose: false};
              }
            }),
          };
        }),
      );
      setlistQuestion(list);
    }
    setloading(false);
  };
  const bottomSheetRef = useRef<any>(null);
  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <ScreenHeader
        action={<View style={{width: 55}} />}
        title={
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text
              style={{
                ...TypoSkin.title3,
                color: ColorThemes.light.Neutral_Text_Color_Title,
              }}>
              Lịch sử làm bài
            </Text>
            <Text
              style={{
                ...TypoSkin.subtitle3,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              }}>
              {`${currentPage + 1}/${listQuestion?.length ?? 0}`}
            </Text>
          </View>
        }
        backIcon={<Winicon src="outline/user interface/e-remove" size={20} />}
        onBack={() => {
          navigateBack();
        }}
      />
      {/* checklist */}
      <View
        style={{
          height: 8,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
          width: '100%',
        }}
      />
      <Pressable
        style={{
          width: '100%',
          flexDirection: 'row',
          flexWrap: 'wrap',
          gap: 16,
          marginVertical: 16,
          paddingHorizontal: 16,
          paddingVertical: 32,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
          alignItems: 'center',
        }}>
        {listQuestion?.map((item, index) => (
          <AppButton
            key={item.Id}
            containerStyle={{
              width: 32,
              height: 32,
              borderRadius: 30,
              justifyContent: 'center',
              alignItems: 'center',
            }}
            backgroundColor={
              item.IsResult
                ? ColorThemes.light.Success_Color_Background
                : ColorThemes.light.Error_Color_Background
            }
            onPress={() => {
              if (pagerRef.current) {
                pagerRef.current.setPage(index); // Navigate to the page
                setCurrentPage(index);
                hideBottomSheet(bottomSheetRef);
              }
            }}
            borderColor="transparent"
            title={`${index + 1}`}
            textColor={
              item.IsResult
                ? ColorThemes.light.Success_Color_Main
                : ColorThemes.light.Error_Color_Main
            }
          />
        ))}
      </Pressable>
      <View
        style={{
          height: 8,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
          width: '100%',
        }}
      />
      {/* content */}
      {loading ? (
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
          }}>
          <ActivityIndicator color={ColorThemes.light.Primary_Color_Main} />
        </View>
      ) : (
        <ContentTest ref={pagerRef} listQuestion={listQuestion} />
      )}

      {/*  */}
      <WScreenFooter
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          alignContent: 'center',
          paddingHorizontal: 16,
        }}>
        {currentPage === 0 ? (
          <View />
        ) : (
          <AppButton
            title={'Câu trước'}
            containerStyle={{}}
            backgroundColor={'transparent'}
            textStyle={TypoSkin.buttonText3}
            borderColor="transparent"
            prefixIconSize={16}
            prefixIcon={'outline/arrows/left-arrow'}
            onPress={previous} // Call previous function
            textColor={ColorThemes.light.Info_Color_Main}
          />
        )}
        {currentPage === listQuestion.length - 1 ? (
          <View />
        ) : (
          <AppButton
            title={`${'Câu sau'}`}
            containerStyle={{}}
            backgroundColor={'transparent'}
            textStyle={TypoSkin.buttonText3}
            borderColor="transparent"
            suffixIconSize={16}
            suffixIcon={'outline/arrows/right-arrow'}
            onPress={async () => {
              if (currentPage >= listQuestion.length) {
                // await onSubmit(
                //   dateStart + examInfor.Time * 60 * 1000,
                //   dateStart,
                //   examInfor.Id,
                // );
                // navigate(RootScreen.resultTest, {id: examInfor.Id});
              } else {
                next();
              }
            }} // Call next function
            textColor={ColorThemes.light.Info_Color_Main}
          />
        )}
      </WScreenFooter>
    </SafeAreaView>
  );
}

const ContentTest = (pros: any) => {
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <PagerView
        ref={pros.ref}
        style={{height: '100%'}}
        initialPage={0}
        scrollEnabled={false}>
        {pros.listQuestion?.map((item: any, index: number) => (
          <View key={index}>
            {/* content */}
            <ScrollView style={{flex: 1}}>
              {/* question */}
              <ListTile
                style={{paddingHorizontal: 16, padding: 0, paddingTop: 16}}
                title={`${index + 1}. ${item.QuestionContent ?? ''}`}
                titleStyle={{
                  ...TypoSkin.title3,
                  fontWeight: '700',
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                }}
              />
              <ListTile
                style={{
                  paddingHorizontal: 16,
                  padding: 0,
                  paddingBottom: 16,
                  paddingTop: 8,
                }}
                title={`(Chọn ${
                  item.SelectionType === 2 ? 'nhiều' : '1'
                } câu trả lời)`}
                titleStyle={{
                  ...TypoSkin.body2,
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                }}
              />
              {/* answers */}
              <View style={{gap: 8}}>
                {item.lstAnswer?.map((answer: any, index: number) => {
                  const colorCheck = item.IsResult
                    ? answer.choose
                      ? ColorThemes.light.Success_Color_Main
                      : 'transparent'
                    : answer.choose
                    ? ColorThemes.light.Error_Color_Main
                    : answer.IsResult
                    ? ColorThemes.light.Success_Color_Main
                    : 'transparent';
                  const colorBackGroundCheck = item.IsResult
                    ? answer.choose
                      ? ColorThemes.light.Success_Color_Border
                      : ColorThemes.light.Neutral_Border_Color_Main
                    : answer.choose
                    ? ColorThemes.light.Error_Color_Border
                    : answer.IsResult
                    ? ColorThemes.light.Success_Color_Border
                    : ColorThemes.light.Neutral_Border_Color_Main;
                  return (
                    <ListTile
                      key={answer.Id}
                      onPress={() => {
                        // dispatch(ExamActions.choose(item.Id, answer.Id));
                        console.log(
                          item.IsResult,
                          answer.choose,
                          colorCheck,
                          item.SelectionType,
                        );
                      }}
                      leading={
                        item.SelectionType == 2 ? (
                          <Checkbox
                            key={answer.Id}
                            value={colorCheck === 'transparent' ? false : true}
                            onChange={() => {
                              // dispatch(ExamActions.choose(item.Id, answer.Id));
                            }}
                            checkboxStyle={{
                              backgroundColor: colorCheck,
                            }}
                          />
                        ) : (
                          <RadioButton.Android
                            key={answer.Id}
                            value={answer.Id}
                            status={
                              colorCheck === 'transparent'
                                ? 'unchecked'
                                : 'checked'
                            }
                            color={colorCheck}
                            onPress={() => {
                              // dispatch(ExamActions.choose(item.Id, answer.Id));
                            }}
                          />
                        )
                      }
                      listtileStyle={{gap: 12, padding: 8}}
                      title={`${answer.Name ?? ''}`}
                      style={{
                        borderColor: colorBackGroundCheck,
                        borderWidth: 1,
                        marginHorizontal: 16,
                        padding: 0,
                      }}
                      titleStyle={{
                        ...TypoSkin.body2,
                        color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                      }}
                    />
                  );
                })}
              </View>
            </ScrollView>
          </View>
        ))}
      </PagerView>
    </View>
  );
};
