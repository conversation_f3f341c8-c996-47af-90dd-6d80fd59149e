/* eslint-disable react-native/no-inline-styles */
import {
  Dimensions,
  Pressable,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import ScreenHeader from '../../../Screen/Layout/header';
import {navigate, navigateBack, RootScreen} from '../../../router/router';
import {
  AppButton,
  FBottomSheet,
  hideBottomSheet,
  showBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import WScreenFooter from '../../../Screen/Layout/footer';
import {TypoSkin} from '../../../assets/skin/typography';
import React, {useEffect, useRef} from 'react';
import {useExamData} from '../../../redux/hook/examHook';
import {useNavigation, useRoute} from '@react-navigation/native';
import {AppDispatch} from '../../../redux/store/store';
import {useDispatch} from 'react-redux';
import {ExamActions} from '../../../redux/reducers/examReducer';
import EmptyPage from '../../../Screen/emptyPage';
import {useTranslation} from 'react-i18next';

export default function ResultTest() {
  const {t} = useTranslation();
  const bottomSheetRef = useRef<any>(null);
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const {id, courseId, type, isCheck} = route.params;
  const {examInfor} = useExamData();
  const {loading} = useExamData();
  const {listQuestion} = useExamData();
  const dispatch: AppDispatch = useDispatch();
  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <ScreenHeader
        title={t('exam.resultsTitle')}
        backIcon={<Winicon src="outline/arrows/left-arrow" size={20} />}
        // onBack={() => {
        //   navigateBack();
        // }}
      />
      {/* content */}
      {isCheck ? (
        <EmptyPage title={t('exam.noAnswers')} />
      ) : (
        <ScrollView
          style={{
            flex: 1,
            backgroundColor:
              (examInfor?.ScoreResult / examInfor?.TotalScore) * 100 >=
              examInfor?.PassingScore
                ? ColorThemes.light.Success_Color_Background
                : ColorThemes.light.Warning_Color_Background,
          }}>
          <View
            style={{
              gap: 16,
              alignContent: 'center',
              alignItems: 'center',
              paddingVertical: 60,
            }}>
            {/* view percent */}
            <View
              style={{
                height: 80,
                width: 80,
                borderRadius: 100,
                backgroundColor:
                  (examInfor?.ScoreResult / examInfor?.TotalScore) * 100 >=
                  examInfor?.PassingScore
                    ? ColorThemes.light.Success_Color_Main
                    : ColorThemes.light.Warning_Color_Main,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text
                style={{
                  ...TypoSkin.title1,
                  color: ColorThemes.light.Neutral_Background_Color_Absolute,
                  textAlign: 'center',
                }}>
                {`${parseFloat(
                  (
                    (examInfor.ScoreResult / examInfor.TotalScore) *
                    100
                  ).toFixed(1),
                )}%`}
              </Text>
            </View>
            <Text
              style={{
                textAlign: 'center',
                ...TypoSkin.title2,
                fontWeight: '700',
                paddingHorizontal: 24,
              }}>
              {(examInfor?.ScoreResult / examInfor?.TotalScore) * 100 >=
              examInfor?.PassingScore
                ? t('exam.congratulations')
                : t('exam.tryAgainWhenReady')}
            </Text>
          </View>
          {/* <View
          style={{
            gap: 16,
            alignContent: 'center',
            alignItems: 'center',
            paddingVertical: 16,
            marginHorizontal: 16,
            paddingHorizontal: 16,
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
            borderRadius: 8,
            flexDirection: 'row',
          }}> */}
          {/* view chart */}
          {/* <View
            style={{
              height: 80,
              width: 80,
              borderRadius: 100,
              backgroundColor: ColorThemes.light.Success_Color_Main,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Text
              style={{
                ...TypoSkin.title1,
                color: ColorThemes.light.Neutral_Background_Color_Absolute,
                textAlign: 'center',
              }}>
              75%
            </Text>
          </View>
        {/* </View> */}
          {/* View result */}
          <Pressable
            style={{
              width: Dimensions.get('screen').width - 32,
              flex: 1,
              flexDirection: 'row',
              flexWrap: 'wrap',
              gap: 12,
              marginHorizontal: 16,
              marginTop: 16,
              padding: 16,
              borderRadius: 8,
              backgroundColor:
                ColorThemes.light.Neutral_Background_Color_Absolute,
            }}>
            {listQuestion.map((item, index) => (
              <AppButton
                key={item.Id}
                containerStyle={{
                  width: 32,
                  height: 32,
                  borderRadius: 30,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={() => {}}
                borderColor="transparent"
                title={`${index + 1}`}
                backgroundColor={
                  item.noResult
                    ? ColorThemes.light.Neutral_Background_Color_Main
                    : item.IsResultTest === true
                    ? ColorThemes.light.Success_Color_Background
                    : ColorThemes.light.Error_Color_Background
                }
                textColor={
                  item.noResult
                    ? ColorThemes.light.Neutral_Text_Color_Body
                    : item.IsResultTest === true
                    ? ColorThemes.light.Success_Color_Main
                    : ColorThemes.light.Error_Color_Main
                }
              />
            ))}
          </Pressable>
        </ScrollView>
      )}
      {/*  */}
      <WScreenFooter
        style={{
          flexDirection: 'row',
          flex: 1,
          gap: 8,
          paddingHorizontal: 16,
        }}>
        <AppButton
          title={t('exam.finish')}
          backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
          textColor={ColorThemes.light.Neutral_Text_Color_Body}
          borderColor="transparent"
          containerStyle={{
            flex: 1,
            borderRadius: 8,
          }}
          onPress={() => {
            dispatch(ExamActions.resetReducer());
            navigateBack();
            // if (type === ExamType.Try) {
            //   navigateReset(RootScreen.navigateESchoolView);
            // } else {
            //   navigateReset(RootScreen.ProccessCourse, {id: courseId});
            // }
          }}
        />
        <AppButton
          title={t('exam.retake')}
          backgroundColor={ColorThemes.light.Primary_Color_Main}
          textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
          borderColor="transparent"
          containerStyle={{
            flex: 1,
            borderRadius: 8,
          }}
          onPress={() => {
            showBottomSheet({
              ref: bottomSheetRef,
              enableDismiss: true,
              title: t('exam.confirmRetake'),
              prefixAction: <View />,
              suffixAction: (
                <TouchableOpacity
                  onPress={() => {
                    hideBottomSheet(bottomSheetRef);
                  }}
                  style={{padding: 6, alignItems: 'center'}}>
                  <Winicon
                    src="outline/layout/xmark"
                    size={20}
                    color={ColorThemes.light.Neutral_Text_Color_Body}
                  />
                </TouchableOpacity>
              ),
              children: (
                <ConFirmReTest ref={bottomSheetRef} id={id} type={type} />
              ),
            });
          }}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
}

const ConFirmReTest = (pros: any) => {
  const {t} = useTranslation();
  const dispatch: AppDispatch = useDispatch();
  const navigation = useNavigation<any>();

  return (
    <View
      style={{
        height: 150,
        width: '100%',
        gap: 16,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        paddingHorizontal: 16,
      }}>
      <AppButton
        title={t('exam.retakeExam')}
        backgroundColor={ColorThemes.light.Primary_Color_Main}
        borderColor="transparent"
        containerStyle={{
          height: 45,
          marginTop: 16,
          borderRadius: 8,
        }}
        onPress={() => {
          dispatch(ExamActions.resetReducer());
          hideBottomSheet(pros.ref);
          // navigate(RootScreen.Test, {id: pros.id, type: pros.type});
          navigation.replace(RootScreen.Test, {id: pros.id, type: pros.type});
        }}
        textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
      />
      <AppButton
        title={t('common.cancel')}
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
        borderColor="transparent"
        containerStyle={{
          height: 45,
          borderRadius: 8,
        }}
        onPress={() => {
          hideBottomSheet(pros.ref);
        }}
        textColor={ColorThemes.light.Neutral_Text_Color_Body}
      />
    </View>
  );
};
