export class StatusOrder {
  static success = 3;
  static proccess = 2;
  static cancel = 4;
}
export class CustomerType {
  static student = 2;
  static teacher = 1;
}
export class CustomerRankType {
  static normal = 2;
  static vip = 1;
}

export class StorageContanst {
  static CustomerId = 'CustomerId';
  static RecentCourse = 'recent_course';
  static accessToken = 'accessToken';
}
export class ExamType {
  static Try = 2;
  static Real = 1;
  static quiz = 3;
}
export class StatusExam {
  static passed = 2;
  static process = 1;
  static fail = 3;
}
export class groupRole {
  static admin = 1;
  static subadmin = 2;
  static member = 3;
}
export class groupMemberRoleStatus {
  static invited = 0;
  static joined = 2;
}
export class FollowStatus {
  static Accept = 1;
  static Reject = 2;
  static Pending = 3;
  static None = 0;
}
export class GameStatus {
  static Completed = 1;
  static Pending = 2;
  static Locked = 3;
}

